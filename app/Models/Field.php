<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Field extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'description',
        'hourly_rate',
        'night_hourly_rate',
        'night_time_start',
        'capacity',
        'status',
        'opening_time',
        'closing_time',
        'min_booking_hours',
        'max_booking_hours',
        'start_date',
        'end_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'night_hourly_rate' => 'decimal:2',
        'min_booking_hours' => 'decimal:1', // Support half-hour increments
        'max_booking_hours' => 'decimal:1', // Support half-hour increments
    ];

    /**
     * Available field types
     */
    public static function getFieldTypes(): array
    {
        return [
            'Soccer' => 'Soccer',
            'Basketball' => 'Basketball',
            'Tennis' => 'Tennis',
            'Volleyball' => 'Volleyball',
            'Baseball' => 'Baseball',
            'Football' => 'Football',
            'Multi-Purpose' => 'Multi-Purpose',
        ];
    }

    /**
     * Available amenities (dynamic from database)
     */
    public static function getAvailableAmenities(): array
    {
        try {
            return \App\Models\Amenity::active()
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();
        } catch (\Exception $e) {
            // Fallback to hardcoded amenities if database is not available
            return [
                'lighting' => 'Lighting',
                'parking' => 'Parking',
                'restrooms' => 'Restrooms',
                'equipment' => 'Equipment Available',
                'scoreboard' => 'Scoreboard',
                'seating' => 'Spectator Seating',
                'sound_system' => 'Sound System',
                'wifi' => 'WiFi',
            ];
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    public static function getAvailableAmenitiesLegacy(): array
    {
        return [
            'lighting' => 'Lighting',
            'parking' => 'Parking',
            'restrooms' => 'Restrooms',
            'equipment' => 'Equipment Available',
            'scoreboard' => 'Scoreboard',
            'seating' => 'Spectator Seating',
            'sound_system' => 'Sound System',
            'wifi' => 'WiFi',
        ];
    }

    /**
     * Available statuses
     */
    public static function getStatuses(): array
    {
        return [
            'Active' => 'Active',
            'Inactive' => 'Inactive',
            // 'Under Maintenance' => 'Under Maintenance',
        ];
    }

    /**
     * Get the bookings for the field (admin system)
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the amenities for the field.
     */
    public function amenities(): BelongsToMany
    {
        return $this->belongsToMany(Amenity::class, 'field_amenity');
    }

    /**
     * Get the utilities for the field.
     */
    public function utilities(): BelongsToMany
    {
        return $this->belongsToMany(Utility::class, 'field_utility')
            ->withTimestamps();
    }

    /**
     * Get reservations for the field (FPMP system)
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Check if field is available for booking at given date and time
     */
    public function isAvailableAt($date, $startTime, $endTime, $excludeBookingId = null): bool
    {
        $query = $this->bookings()
            ->whereDate('booking_date', $date)
            ->where('status', '!=', 'Cancelled')
            ->where(function ($q) use ($startTime, $endTime) {
                // Check for overlapping time slots
                // Two time slots overlap if:
                // 1. Existing start_time is before new end_time AND existing end_time is after new start_time
                $q->where(function ($q2) use ($startTime, $endTime) {
                    $q2->where('start_time', '<', $endTime)
                        ->where('end_time', '>', $startTime);
                });
            });

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->count() === 0;
    }

    /**
     * Check if time is within working hours
     */
    public function isWithinWorkingHours($startTime, $endTime): bool
    {
        // Convert times to Carbon instances for proper comparison
        $start = \Carbon\Carbon::parse($startTime);
        $end = \Carbon\Carbon::parse($endTime);
        // Use robust parsing for DB time columns which may be returned as "H:i:s"
        $opening = \Carbon\Carbon::parse($this->opening_time);
        $closing = \Carbon\Carbon::parse($this->closing_time);

        // Check if start time is within working hours
        if ($start->lt($opening) || $start->gte($closing)) {
            return false;
        }

        // Check if end time is within working hours
        // Handle case where booking spans midnight (not allowed for now)
        if ($end->lt($start)) {
            return false; // Booking spans midnight, not allowed
        }

        return $end->lte($closing);
    }

    /**
     * Validate booking duration (supports half-hour increments)
     */
    public function isValidDuration($hours): bool
    {
        // Convert to float to handle decimal values
        $hours = (float) $hours;

        // Check if duration is within min/max bounds
        if ($hours < $this->min_booking_hours || $hours > $this->max_booking_hours) {
            return false;
        }

        // Ensure duration is in valid half-hour increments (0.5, 1.0, 1.5, etc.)
        return fmod($hours * 2, 1) == 0; // Check if hours * 2 is a whole number
    }

    /**
     * Get available time slots for a given date (30-minute intervals)
     */
    public function getAvailableTimeSlots($date): array
    {
        $slots = [];
        // Use robust parsing for DB time columns which may be returned as "H:i:s"
        $openingTime = Carbon::parse($this->opening_time);
        $closingTime = Carbon::parse($this->closing_time);

        // Generate 30-minute time slots
        $current = $openingTime->copy();

        while ($current->lt($closingTime)) {
            $startTime = $current->format('H:i');
            $endTime = $current->copy()->addMinutes(30)->format('H:i');

            // Only include slots that don't extend past closing time
            if (Carbon::parse($endTime)->lte($closingTime)) {
                if ($this->isAvailableAt($date, $startTime, $endTime)) {
                    $slots[] = [
                        'start_time' => $startTime,
                        'end_time' => $endTime,
                        'display' => date('g:i A', strtotime($startTime)),
                    ];
                }
            }

            $current->addMinutes(30);
        }

        return $slots;
    }

    /**
     * Get active bookings for the field
     */
    public function activeBookings(): HasMany
    {
        return $this->hasMany(Reservation::class)->whereIn('status', ['Pending', 'Confirmed']);
    }

    /**
     * Check if field is available for a specific date and time range
     */
    public function isAvailable(string $date, string $startTime, string $endTime, ?int $excludeBookingId = null): bool
    {
        $query = $this->bookings()
            ->whereDate('booking_date', $date)
            ->whereIn('status', ['Pending', 'Confirmed'])
            ->where(function ($q) use ($startTime, $endTime) {
                // Check for overlapping time slots
                // Two time slots overlap if:
                // 1. Existing start_time is before new end_time AND existing end_time is after new start_time
                $q->where(function ($q2) use ($startTime, $endTime) {
                    $q2->where('start_time', '<', $endTime)
                        ->where('end_time', '>', $startTime);
                });
            });

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->count() === 0;
    }

    /**
     * Get formatted amenities list
     */
    public function getFormattedAmenitiesAttribute(): string
    {
        if (! $this->amenities || $this->amenities->isEmpty()) {
            return 'None';
        }

        $amenityNames = $this->amenities->pluck('name')->toArray();

        return implode(', ', $amenityNames);
    }

    /**
     * Scope for active fields
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    /**
     * Scope for available fields (active and not under maintenance)
     */
    public function scopeAvailable($query)
    {
        return $query->whereIn('status', ['Active']);
    }

    /**
     * Check if a given time is during night hours
     */
    public function isNightTime(string $time): bool
    {
        if (! $this->night_time_start) {
            return false;
        }

        $checkTime = \Carbon\Carbon::parse($time);

        // Handle different formats of night_time_start
        if (is_string($this->night_time_start)) {
            if (strlen($this->night_time_start) === 5) {
                $nightStart = \Carbon\Carbon::parse($this->night_time_start);
            } else {
                $nightStart = \Carbon\Carbon::parse($this->night_time_start);
            }
        } else {
            $nightStart = $this->night_time_start;
        }

        // For business hours, night time typically runs from evening until early morning
        // Default assumption: night time ends at 6:00 AM (can be made configurable later)
        $nightEnd = \Carbon\Carbon::parse('06:00');

        // If night start is in the evening (after 12:00), night time spans midnight
        if ($nightStart->hour >= 12) {
            // Night time spans midnight (e.g., 18:00 to 06:00 next day)
            // Time is night if: time >= nightStart OR time < nightEnd
            return $checkTime->gte($nightStart) || $checkTime->lt($nightEnd);
        } else {
            // Night start is in early morning (unusual but possible)
            // Night time is same day (e.g., 02:00 to 06:00)
            return $checkTime->gte($nightStart) && $checkTime->lt($nightEnd);
        }
    }

    /**
     * Get the appropriate hourly rate for a given time
     */
    public function getHourlyRateForTime(string $time): float
    {
        if ($this->night_hourly_rate && $this->isNightTime($time)) {
            return (float) $this->night_hourly_rate;
        }

        return (float) $this->hourly_rate;
    }

    /**
     * Calculate total cost for a booking period (supports half-hour increments)
     */
    public function calculateBookingCost(string $startTime, string $endTime): float
    {
        $start = \Carbon\Carbon::parse($startTime);
        $end = \Carbon\Carbon::parse($endTime);

        // Handle midnight crossing: if end time is earlier than start time, it's next day
        if ($end->lt($start)) {
            $end->addDay();
        }

        $totalCost = 0;
        $current = $start->copy();

        // Calculate cost in 30-minute increments
        while ($current->lt($end)) {
            // Use only time for rate calculation (ignore date component)
            $timeForRate = $current->format('H:i');
            $hourlyRate = $this->getHourlyRateForTime($timeForRate);

            // Calculate the duration of this segment (30 minutes or remaining time if less)
            $segmentEnd = $current->copy()->addMinutes(30);
            if ($segmentEnd->gt($end)) {
                $segmentEnd = $end->copy();
            }

            // Calculate the fraction of an hour for this segment
            $segmentMinutes = $current->diffInMinutes($segmentEnd);
            $segmentHours = $segmentMinutes / 60;

            $totalCost += $hourlyRate * $segmentHours;
            $current->addMinutes(30);
        }

        return round($totalCost, 2);
    }

    /**
     * Get formatted day rate
     */
    public function getFormattedDayRateAttribute(): string
    {
        return 'XCG '.number_format($this->hourly_rate, 2);
    }

    /**
     * Get formatted night rate
     */
    public function getFormattedNightRateAttribute(): string
    {
        if ($this->night_hourly_rate === null) {
            return 'Same as day rate';
        }

        return 'XCG '.number_format($this->night_hourly_rate, 2);
    }

    /**
     * Get formatted night time start
     */
    public function getFormattedNightTimeStartAttribute(): string
    {
        if (! $this->night_time_start) {
            return '6:00 PM';
        }

        // Handle both time string and datetime object
        if (is_string($this->night_time_start)) {
            // If it's a time string like "18:00"
            if (strlen($this->night_time_start) === 5) {
                return \Carbon\Carbon::parse($this->night_time_start)->format('g:i A');
            }

            // If it's a full datetime string
            return \Carbon\Carbon::parse($this->night_time_start)->format('g:i A');
        }

        // If it's already a Carbon instance
        return $this->night_time_start->format('g:i A');
    }

    // pa wak e periode di e field si e ta den vacation mode
    public function updateStatusBasedOnDates()
    {
        $today = Carbon::today();

        // If field is inactive, do nothing
        if ($this->status === 'Inactive') {
            return;
        }

        if ($this->start_date && $this->end_date) {
            // Check if today is between the start and end date (inclusive)
            if ($today->between($this->start_date, $this->end_date)) {
                $this->status = 'Under Maintenance';
            } else {
                $this->status = 'Active';
            }
        } else {
            // No maintenance period set, default to active
            $this->status = 'Active';
        }

        $this->save();
    }

    public function vacationOrMaintenance($date)
    {
        if (!$this->start_date || !$this->end_date) {
            // No maintenance/vacation period set
            return false;
        }

        $checkDate = $date instanceof Carbon ? $date : Carbon::parse($date);

        return $checkDate->between(Carbon::parse($this->start_date), Carbon::parse($this->end_date));
    }
}
